import type { ClientReadableStream, StatusObject } from '@grpc/grpc-js'
import { Emitter } from '@kdt310722/utils/event'
import { tap } from '@kdt310722/utils/function'
import type { Awaitable } from '@kdt310722/utils/promise'
import { StreamError } from '../errors'
import { isAbortError } from '../utils/errors'
import { createDeferredWithTimeout } from '../utils/promises'

export enum StreamState {
    CLOSED = 'CLOSED',
    SUBSCRIBING = 'SUBSCRIBING',
    SUBSCRIBED = 'SUBSCRIBED',
}

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
}

export type StreamWrapperEvents = {}

export class StreamWrapper<TResponse, TStream extends ClientReadableStream<TResponse> = ClientReadableStream<TResponse>> extends Emitter<StreamWrapperEvents> {
    protected readonly closeTimeout: number
    protected readonly subscribeTimeout: number

    protected stream?: TStream

    #state: StreamState = StreamState.CLOSED

    public constructor(protected readonly subscriber: (signal: AbortSignal) => Awaitable<TStream>, { timeout = {} }: StreamWrapperOptions = {}) {
        super()

        this.subscribeTimeout = timeout.subscribe ?? 10_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
    }

    public get state() {
        return this.#state
    }

    public async subscribe(signal?: AbortSignal) {
        if (signal?.aborted) {
            throw new StreamError('Subscription aborted before it started')
        }

        if (this.#state !== StreamState.CLOSED) {
            throw new StreamError(`Cannot subscribe while stream is in state: ${StreamState[this.#state]}`)
        }

        this.setState(StreamState.SUBSCRIBING)

        const abortController = new AbortController()
        const abortSignal = AbortSignal.any([abortController.signal, ...(signal ? [signal] : [])])
        const promise = createDeferredWithTimeout<void>(this.subscribeTimeout, () => tap(new StreamError('Subscribe stream timeout'), () => abortController.abort()))

        const handleAbort = () => {
            if (!promise.isSettled) {
                promise.reject(abortSignal.reason)
            }
        }

        let cleanup: () => void

        const handlers = {
            metadata: () => !promise.isSettled && promise.resolve(),
            status: (status: StatusObject) => (promise.isSettled ? this.handleStatus(status) : promise.reject(new StreamError('Stream status received before subscription completed').withValue('status', status))),
            error: (error: unknown) => (promise.isSettled ? this.handleError(error) : promise.reject(error)),
            close: () => {
                cleanup()

                if (promise.isSettled) {
                    this.handleClose()
                } else {
                    promise.reject(new StreamError('Stream closed before subscription completed'))
                }
            },
        }

        abortSignal.addEventListener('abort', handleAbort, { once: true })

        cleanup = () => {
            abortSignal.removeEventListener('abort', handleAbort)

            for (const [event, handler] of Object.entries(handlers)) {
                this.stream?.removeListener(event, handler)
            }
        }

        try {
            this.stream = await this.subscriber(abortSignal)

            for (const [event, handler] of Object.entries(handlers)) {
                this.stream.on(event, handler)
            }
        } catch (error) {
            if (!isAbortError(error) && !promise.isSettled) {
                promise.reject(error)
            }
        }

        try {
            await promise.then(() => this.setState(StreamState.SUBSCRIBED))
        } catch (error) {
            this.setState(StreamState.CLOSED)
            this.stream = undefined

            throw error
        } finally {
            cleanup()
        }
    }

    public async close() {}

    protected handleStatus(status: StatusObject) {}

    protected handleError(error: unknown) {}

    protected handleClose() {}

    protected setState(state: StreamState) {
        this.#state = state
    }
}
