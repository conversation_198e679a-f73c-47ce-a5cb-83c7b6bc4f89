import type { ClientReadableStream } from '@grpc/grpc-js'
import type { Emitter } from '@kdt310722/utils/event'
import type { Awaitable } from '@kdt310722/utils/promise'

export type StreamManagerEvents = {}

export class StreamManager<TData, TStream extends ClientReadableStream<TData> = ClientReadableStream<TData>> {
    public constructor(protected readonly subscriber: () => Awaitable<TStream>, protected readonly emitter: Emitter<StreamManagerEvents>) {}

    public async create() {}
}
