import { notNullish } from '@kdt310722/utils/common'
import type { SubscribeRequest } from '../generated/geyser'

export const createSubscribeRequest = (request: Partial<SubscribeRequest>): SubscribeRequest => ({
    accounts: {},
    slots: {},
    transactions: {},
    transactionsStatus: {},
    blocks: {},
    blocksMeta: {},
    entry: {},
    accountsDataSlice: [],
    ...request,
})

export function createPingMessage(id = 1) {
    return createSubscribeRequest({ ping: { id } })
}

export function isPingRequestMessage(request: SubscribeRequest) {
    return notNullish(request.ping?.id)
}
