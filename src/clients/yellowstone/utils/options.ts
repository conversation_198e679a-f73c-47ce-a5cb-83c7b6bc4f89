import { resolveNestedOptions } from '@kdt310722/utils/object'
import type { HeartbeatManagerOptions } from '../../../utils'
import type { SubscribeRequest } from '../generated/geyser'
import { createPingMessage } from './requests'

export function resolveHeartbeatOptions(options: HeartbeatManagerOptions<SubscribeRequest> | boolean) {
    const resolved = resolveNestedOptions(options) || { enabled: false }
    const getMessage = resolved.message

    resolved.message = () => {
        return getMessage?.() ?? { data: createPingMessage() }
    }

    return resolved
}
