import type { CallOptions, ClientDuplexStream, Metadata } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import type { DeferredPromise } from '@kdt310722/utils/promise'
import { DuplexStreamWrapper, type DuplexStreamWrapperOptions, EMPTY_METADATA, type OnStreamChangeFn } from '../../../utils'
import type { GeyserClient, SubscribeRequest, SubscribeUpdate } from '../generated/geyser'
import { resolveHeartbeatOptions } from './options'
import { createPingMessage, isPingRequestMessage } from './requests'

export type YellowstoneGeyserSubscribeStream = ClientDuplexStream<SubscribeRequest, SubscribeUpdate>

export interface YellowstoneGeyserStreamWrapperOptions extends DuplexStreamWrapperOptions<SubscribeRequest> {
    metadata?: Metadata
    callOptions?: CallOptions
    updateOnResubscribed?: boolean
}

export class YellowstoneGeyserStreamWrapper extends DuplexStreamWrapper<SubscribeRequest, SubscribeUpdate> {
    protected lastRequest?: SubscribeRequest

    public constructor(protected readonly client: GeyserClient, { metadata = EMPTY_METADATA, callOptions, heartbeat = true, updateOnResubscribed = true, ...options }: YellowstoneGeyserStreamWrapperOptions = {}) {
        super(() => this.client.subscribe(metadata, callOptions), { heartbeat: resolveHeartbeatOptions(heartbeat), ...options })

        this.on('resubscribed', () => updateOnResubscribed && this.update().catch((error) => this.getStream().then((stream) => {
            this.handleError(stream, error)
        })))
    }

    public async update() {
        if (notNullish(this.lastRequest)) {
            return this.write(this.lastRequest)
        }
    }

    public async ping(id?: number) {
        return this.write(createPingMessage(id))
    }

    public override async write(data: SubscribeRequest) {
        return super.write(data).then(() => {
            if (!isPingRequestMessage(data)) {
                this.lastRequest = data
            }
        })
    }

    protected override registerStreamListeners(promise: DeferredPromise<void>, stream: YellowstoneGeyserSubscribeStream, isExplicitlyClosed: () => boolean, onStreamChange?: OnStreamChangeFn<YellowstoneGeyserSubscribeStream>, afterCleanup?: () => void) {
        return super.registerStreamListeners(promise, stream, isExplicitlyClosed, onStreamChange, () => {
            if (isExplicitlyClosed()) {
                this.lastRequest = undefined
            }

            afterCleanup?.()
        })
    }
}
