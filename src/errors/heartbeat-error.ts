import { BaseError } from './base-error'

export type HeartbeatAction = 'ping' | 'pong' | 'timeout' | 'close'

export class HeartbeatError extends BaseError {
    public declare interval?: number
    public declare timeout?: number
    public declare action?: HeartbeatAction

    public withInterval(interval: number) {
        return this.withValue('interval', interval)
    }

    public withTimeout(timeout: number) {
        return this.withValue('timeout', timeout)
    }

    public withAction(action: HeartbeatAction) {
        return this.withValue('action', action)
    }
}
