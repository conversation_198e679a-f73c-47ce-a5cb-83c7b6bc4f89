import type { ClientReadableStream, StatusObject } from '@grpc/grpc-js'
import { isNullish, notNullish } from '@kdt310722/utils/common'
import { Emitter } from '@kdt310722/utils/event'
import { tryCatch } from '@kdt310722/utils/function'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, type DeferredPromise, createDeferred, tap, withTimeout } from '@kdt310722/utils/promise'
import { IdleTimeoutError, StreamError } from '../../errors'
import { isIgnorableGrpcError, isIgnorableGrpcStatus } from '../grpc'
import { ResubscribeManager, type ResubscribeManagerEvents, type ResubscribeManagerOptions } from './resubscribe-manager'
import type { OnStreamChangeFn, StreamWithClose } from './types'

export interface StreamWrapperTimeoutOptions {
    subscribe?: number
    close?: number
    idle?: number
}

export interface StreamWrapperOptions {
    timeout?: StreamWrapperTimeoutOptions
    resubscribe?: ResubscribeManagerOptions | boolean
    closeOnError?: boolean
    destroyOnCloseFail?: boolean
}

export type StreamWrapperEvents<TData> = ResubscribeManagerEvents & {
    subscribed: () => void
    closed: (isExplicitly: boolean) => void
    data: (data: TData) => void
    closeError: (error: unknown) => void
    status: (status: StatusObject) => void
    pause: () => void
    resume: () => void
}

export class StreamWrapper<TData, TStream extends ClientReadableStream<TData> = ClientReadableStream<TData>> extends Emitter<StreamWrapperEvents<TData>> {
    protected readonly resubscribeManager: ResubscribeManager<TStream>

    protected readonly subscribeTimeout: number
    protected readonly closeTimeout: number
    protected readonly idleTimeout: number

    protected readonly closeOnError: boolean
    protected readonly destroyOnCloseFail: boolean

    protected stream?: Promise<StreamWithClose<TStream>>
    protected closePromise?: Promise<void>

    protected idleTimer?: NodeJS.Timeout
    protected lastDataTime?: number

    public constructor(protected readonly subscriber: () => Awaitable<TStream>, { timeout = {}, closeOnError = true, destroyOnCloseFail = true, resubscribe = true }: StreamWrapperOptions = {}) {
        super()

        this.subscribeTimeout = timeout.subscribe ?? 30_000
        this.closeTimeout = timeout.close ?? this.subscribeTimeout
        this.idleTimeout = timeout.idle ?? 60_000
        this.closeOnError = closeOnError
        this.destroyOnCloseFail = destroyOnCloseFail
        this.resubscribeManager = new ResubscribeManager<TStream>(resolveNestedOptions(resubscribe) || { enabled: false }, this, this.createStream.bind(this))
    }

    public async subscribe() {
        if (notNullish(this.closePromise)) {
            throw new StreamError('Streaming is currently closing')
        }

        if (notNullish(this.stream)) {
            throw new StreamError('Stream is already subscribed')
        }

        await (this.stream = this.createStream((stream) => this.stream = notNullish(stream) ? Promise.resolve(stream) : undefined).then(tap(() => this.emit('subscribed'))))
    }

    public async close(isExplicitly?: boolean, destroyOnCloseFail?: boolean) {
        if (notNullish(this.closePromise)) {
            throw new StreamError('Streaming is already closing')
        }

        return this.closePromise = this.getStream().then(async (stream) => stream.close(isExplicitly, destroyOnCloseFail).finally(() => {
            this.stream = undefined
            this.closePromise = undefined
        }))
    }

    protected async getStream() {
        if (isNullish(this.stream)) {
            throw new StreamError('Stream is not subscribed')
        }

        return this.stream
    }

    protected async createStream(onStreamChange?: OnStreamChangeFn<TStream>): Promise<StreamWithClose<TStream>> {
        let isExplicitlyClosed = false

        const promise = createDeferred<void>()
        const stream = await this.subscriber()

        const cleanup = this.registerStreamListeners(promise, stream, () => isExplicitlyClosed, onStreamChange, () => {
            this.resetIdleTimer()
            this.lastDataTime = undefined
        })

        await withTimeout(promise, this.subscribeTimeout, () => new StreamError('Subscribe timeout')).catch((error) => {
            cleanup()
            tryCatch(() => stream.destroy(), null)

            throw error
        })

        this.runIdleTimer(stream)

        const close = async (isExplicitly = true, destroyOnCloseFail?: boolean) => {
            return Promise.resolve(isExplicitlyClosed = isExplicitly).then(() => this.closeStream(stream, destroyOnCloseFail))
        }

        return Object.assign(stream, { close })
    }

    protected registerStreamListeners(promise: DeferredPromise<void>, stream: TStream, isExplicitlyClosed: () => boolean, onStreamChange?: OnStreamChangeFn<TStream>, afterCleanup?: () => void) {
        let metadataHandler: () => void
        let statusHandler: (status: StatusObject) => void
        let errorHandler: (error: Error) => void
        let closeHandler: () => void
        let endHandler: () => void
        let dataHandler: (data: TData) => void
        let pauseHandler: () => void
        let resumeHandler: () => void

        const cleanup = () => {
            stream.removeListener('metadata', metadataHandler)
            stream.removeListener('status', statusHandler)
            stream.removeListener('error', errorHandler)
            stream.removeListener('close', closeHandler)
            stream.removeListener('end', endHandler)
            stream.removeListener('data', dataHandler)
            stream.removeListener('pause', pauseHandler)
            stream.removeListener('resume', resumeHandler)

            afterCleanup?.()
        }

        stream.once('metadata', metadataHandler = () => promise.isSettled || promise.resolve())
        stream.once('status', statusHandler = (status) => (promise.isSettled ? this.handleStatus(stream, status) : promise.reject(new StreamError('Stream status received before subscription').withValue('status', status))))

        stream.on('pause', pauseHandler = () => promise.isSettled && this.handlePause(stream))
        stream.on('resume', resumeHandler = () => promise.isSettled && this.handleResume(stream))
        stream.on('error', errorHandler = (error) => (promise.isSettled ? this.handleError(stream, error) : promise.reject(error)))
        stream.on('close', closeHandler = () => (promise.isSettled ? this.handleClose(stream, cleanup, isExplicitlyClosed(), onStreamChange) : promise.reject(new StreamError('Stream closed unexpectedly'))))
        stream.on('end', endHandler = () => (promise.isSettled || promise.reject(new StreamError('Stream ended unexpectedly'))))
        stream.on('data', dataHandler = (data) => (promise.isSettled ? this.handleData(stream, data) : promise.resolve()))

        return cleanup
    }

    protected handlePause(_stream: TStream) {
        this.resetIdleTimer()
        this.emit('pause')
    }

    protected handleResume(stream: TStream) {
        this.runIdleTimer(stream)
        this.emit('resume')
    }

    protected async closeStream(stream: TStream, destroyOnCloseFail = false) {
        if (stream.readableEnded) {
            return
        }

        const promise = createDeferred<void>()
        const closeHandler = () => promise.resolve()
        const errorHandler = (error: unknown) => promise.reject(error)

        stream.once('end', closeHandler)
        stream.once('close', closeHandler)
        stream.once('error', errorHandler)
        stream.cancel()

        const handleError = (error: Error) => {
            if (this.destroyOnCloseFail || destroyOnCloseFail) {
                stream.destroy(error)
            } else {
                throw error
            }
        }

        await withTimeout(promise, this.closeTimeout, () => new StreamError('Close timeout')).catch(handleError).finally(() => {
            stream.removeListener('end', closeHandler)
            stream.removeListener('close', closeHandler)
            stream.removeListener('error', errorHandler)
        })
    }

    protected handleStatus(_stream: TStream, status: StatusObject) {
        this.resubscribeManager.setLatestStatus(status)

        if (!isIgnorableGrpcStatus(status)) {
            this.emit('status', status)
        }
    }

    protected handleData(stream: TStream, data: TData) {
        this.emitData(data)
        this.lastDataTime = Date.now()
        this.runIdleTimer(stream)
    }

    protected emitData(data: TData) {
        this.emit('data', data)
    }

    protected runIdleTimer(stream: TStream) {
        if (this.idleTimer) {
            clearTimeout(this.idleTimer)
        }

        this.idleTimer = setTimeout(() => this.handleError(stream, new IdleTimeoutError().withLastDataTime(this.lastDataTime)), this.idleTimeout)
    }

    protected handleError(stream: TStream, error: unknown) {
        if (isIgnorableGrpcError(error)) {
            return
        }

        this.emit('error', this.resubscribeManager.setLatestError(error))

        if (this.closeOnError) {
            this.closeStream(stream).catch((closeError) => this.emit('closeError', closeError))
        }
    }

    protected handleClose(stream: TStream, cleanup: () => void, isExplicitly: boolean, onStreamChange?: OnStreamChangeFn<TStream>) {
        cleanup()
        this.emit('closed', isExplicitly)

        if (isExplicitly) {
            this.resubscribeManager.reset()
        } else {
            this.resubscribeManager.handleResubscribe(stream, onStreamChange)
        }
    }

    protected resetIdleTimer() {
        if (this.idleTimer) {
            clearTimeout(this.idleTimer)
            this.idleTimer = undefined
        }
    }
}
