import type { ClientDuplexStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import type { Awaitable } from '@kdt310722/utils/promise'
import { type BaseErrorOptions, type HeartbeatAction, HeartbeatError } from '../../errors'
import type { DuplexStreamWrapper } from './duplex-stream-wrapper'

export interface HeartbeatMessage<TRequest> {
    data: TRequest
    encoding?: string
}

export interface HeartbeatManagerOptions<TRequest> {
    enabled?: boolean
    interval?: number
    timeout?: number
    message?: () => Awaitable<HeartbeatMessage<TRequest>>
}

export class HeartbeatManager<TRequest, TResponse> {
    protected readonly isEnabled: boolean
    protected readonly interval: number
    protected readonly timeout: number
    protected readonly message?: () => Awaitable<HeartbeatMessage<TRequest>>

    protected pingTimer?: NodeJS.Timeout
    protected pongTimer?: NodeJS.Timeout

    public constructor(protected readonly stream: DuplexStreamWrapper<TRequest, TResponse>, { enabled = true, interval = 30_000, timeout = 10_000, message }: HeartbeatManagerOptions<TRequest>) {
        this.isEnabled = enabled && notNullish(message) && interval > 0 && timeout > 0
        this.interval = interval
        this.timeout = timeout
        this.message = message
    }

    public start(stream: ClientDuplexStream<TRequest, TResponse>) {
        if (this.isEnabled) {
            this.pingTimer = setInterval(() => this.send(stream), this.interval)
        }
    }

    public stop() {
        this.reset()

        if (this.pingTimer) {
            clearInterval(this.pingTimer)
            this.pingTimer = undefined
        }
    }

    public reset() {
        if (this.pongTimer) {
            clearTimeout(this.pongTimer)
            this.pongTimer = undefined
        }
    }

    protected async send(stream: ClientDuplexStream<TRequest, TResponse>) {
        if (!this.isEnabled || !this.message) {
            return
        }

        try {
            const message = await this.message()

            await this.stream.write(message.data, message.encoding).catch((error) => {
                throw this.createError('ping', 'Failed to send heartbeat message', { cause: error }).withValue('sendMessage', message)
            })

            this.pongTimer = setTimeout(() => stream.destroy(this.createError('timeout', 'Heartbeat response timed out')), this.timeout)
        } catch (error) {
            stream.destroy(error instanceof Error ? error : this.createError('ping', 'Failed to send heartbeat message', { cause: error }))
        }
    }

    protected createError(action: HeartbeatAction, message: string, options?: BaseErrorOptions) {
        return new HeartbeatError(message, options).withTimeout(this.timeout).withInterval(this.interval).withAction(action)
    }
}
