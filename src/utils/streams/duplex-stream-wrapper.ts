import type { ClientDuplexStream } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { type Awaitable, type DeferredPromise, createDeferred, tap, withTimeout } from '@kdt310722/utils/promise'
import { StreamError } from '../../errors'
import { HeartbeatManager, type HeartbeatManagerOptions } from './heartbeat-manager'
import { StreamWrapper, type StreamWrapperOptions, type StreamWrapperTimeoutOptions } from './stream-wrapper'
import type { OnStreamChangeFn } from './types'

export interface DuplexStreamWrapperTimeoutOptions extends StreamWrapperTimeoutOptions {
    end?: number
    drain?: number
    write?: number
}

export interface DuplexStreamWrapperOptions<TRequest> extends Omit<StreamWrapperOptions, 'timeout'> {
    timeout?: DuplexStreamWrapperTimeoutOptions
    heartbeat?: HeartbeatManagerOptions<TRequest> | boolean
}

export class DuplexStreamWrapper<TRequest, TResponse, TStream extends ClientDuplexStream<TRequest, TResponse> = ClientDuplexStream<TRequest, TResponse>> extends StreamWrapper<TResponse, TStream> {
    protected readonly endTimeout: number
    protected readonly drainTimeout: number
    protected readonly writeTimeout: number
    protected readonly heartbeatManager: HeartbeatManager<TRequest, TResponse>

    public constructor(protected override readonly subscriber: () => Awaitable<TStream>, { heartbeat = true, ...options }: DuplexStreamWrapperOptions<TRequest> = {}) {
        super(subscriber, options)

        this.endTimeout = options.timeout?.end ?? 10_000
        this.drainTimeout = options.timeout?.drain ?? 10_000
        this.writeTimeout = options.timeout?.write ?? 10_000
        this.heartbeatManager = new HeartbeatManager<TRequest, TResponse>(this, resolveNestedOptions(heartbeat) || { enabled: false })
    }

    public async write(data: TRequest, encoding?: string) {
        this.emit('write', data, encoding)

        const stream = await this.getStream().then((stream) => this.waitForWritable(stream))
        const wrote = createDeferred<void>()
        const result = stream.write(data, encoding, (error?: Error) => (notNullish(error) ? wrote.reject(error) : wrote.resolve()))

        let errorHandler: ((error: unknown) => void) | undefined

        if (result) {
            stream.once('error', errorHandler = (error) => wrote.isSettled || wrote.reject(error))
        } else if (!wrote.isSettled) {
            wrote.reject(new StreamError('Stream is not writable'))
        }

        return withTimeout(wrote, this.writeTimeout, () => new StreamError('Write timeout')).finally(() => {
            if (errorHandler) {
                stream.removeListener('error', errorHandler)
            }
        })
    }

    protected override async createStream(onStreamChange?: OnStreamChangeFn<TStream>) {
        return super.createStream(onStreamChange).then(tap((stream) => this.heartbeatManager.start(stream)))
    }

    protected override handleData(stream: TStream, data: TResponse) {
        this.heartbeatManager.reset()
        super.handleData(stream, data)
    }

    protected async waitForWritable(stream: TStream) {
        if (this.isWritable(stream)) {
            return stream
        }

        const writable = createDeferred<void>()

        const handleDrain = () => {
            writable.resolve()
        }

        stream.once('drain', handleDrain)

        return withTimeout(writable, this.drainTimeout, () => new StreamError('Drain timeout')).then(() => stream).finally(() => {
            stream.removeListener('drain', handleDrain)
        })
    }

    protected override registerStreamListeners(promise: DeferredPromise<void>, stream: TStream, isExplicitlyClosed: () => boolean, onStreamChange?: OnStreamChangeFn<TStream>, afterCleanup?: () => void) {
        let finishHandler: () => void

        stream.on('finish', finishHandler = () => {
            if (!promise.isSettled) {
                promise.reject(new StreamError('Stream finished unexpectedly'))
            }
        })

        return super.registerStreamListeners(promise, stream, isExplicitlyClosed, onStreamChange, () => {
            stream.removeListener('finish', finishHandler)
            this.heartbeatManager.stop()
            afterCleanup?.()
        })
    }

    protected override async closeStream(stream: TStream) {
        return this.end(stream).then(() => super.closeStream(stream))
    }

    protected async end(stream: TStream) {
        if (!this.isWritable(stream) || stream.writableFinished) {
            return
        }

        const promise = createDeferred<void>()
        const finishHandler = () => promise.resolve()
        const errorHandler = (error: Error) => promise.reject(error)

        stream.once('finish', finishHandler)
        stream.once('end', finishHandler)
        stream.once('close', finishHandler)
        stream.once('error', errorHandler)

        stream.end()

        await withTimeout(promise, this.endTimeout, () => new StreamError('End timeout')).finally(() => {
            stream.removeListener('finish', finishHandler)
            stream.removeListener('end', finishHandler)
            stream.removeListener('close', finishHandler)
            stream.removeListener('error', errorHandler)
        })
    }

    protected isWritable(stream: TStream) {
        return !stream.writableEnded && stream.writable
    }
}
