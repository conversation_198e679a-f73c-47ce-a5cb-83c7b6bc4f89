import type { ClientReadableStream } from '@grpc/grpc-js'
import { StreamError } from '../../errors'
import { createDeferredWithTimeout } from '../promises'

export interface CloseStreamOptions {
    timeout?: number
    destroyOnFail?: boolean
    onError?: (error: unknown) => void
}

export async function closeStream<T>(stream: ClientReadableStream<T>, options: CloseStreamOptions = {}) {
    const { timeout = 10_000, destroyOnFail = true, onError } = options

    if (stream.closed || stream.destroyed || stream.readableEnded) {
        return
    }

    const promise = createDeferredWithTimeout<void>(timeout, () => new StreamError('Stream close timeout'))
    const closeHandler = () => !promise.isSettled && promise.resolve()
    const errorHandler = (error: unknown) => !promise.isSettled && promise.reject(error)

    stream.once('end', closeHandler)
    stream.once('close', closeHandler)
    stream.once('error', errorHandler)

    try {
        stream.cancel()
    } catch (error) {
        if (!promise.isSettled) {
            promise.reject(error)
        }
    }

    try {
        await promise
    } catch (error) {
        onError?.(error)

        if (destroyOnFail) {
            stream.destroy()
        } else {
            throw error
        }
    } finally {
        stream.removeListener('end', closeHandler)
        stream.removeListener('close', closeHandler)
        stream.removeListener('error', errorHandler)
    }
}
