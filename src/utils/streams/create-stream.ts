import type { ClientReadableStream, Metadata, StatusObject } from '@grpc/grpc-js'
import { type Fn, tap } from '@kdt310722/utils/function'
import type { Awaitable } from '@kdt310722/utils/promise'
import { StreamError } from '../../errors'
import { isAbortError } from '../errors'
import { createDeferredWithTimeout } from '../promises'

export interface CreateStreamOptions<TData> {
    timeout?: number
    signal?: AbortSignal
    onMetadata?: (metadata: Metadata) => void
    onStatus?: (status: StatusObject) => void
    onError?: (error: unknown) => void
    onClose?: () => void
    onEnd?: () => void
    onData?: (data: TData) => void
    onPause?: () => void
    onResume?: () => void
}

export async function subscribeStream<TData, TStream extends ClientReadableStream<TData> = ClientReadableStream<TData>>(createStream: (signal: AbortSignal) => Awaitable<TStream>, { timeout = 30_000, signal, onMetadata, onStatus, onError, onClose, onEnd, onData, onPause, onResume }: CreateStreamOptions<TData> = {}): Promise<TStream> {
    if (signal?.aborted) {
        throw new StreamError('Subscription aborted before it started')
    }

    let cleanup: (() => void) | undefined

    const abortController = new AbortController()
    const abortSignal = AbortSignal.any([abortController.signal, ...(signal ? [signal] : [])])
    const promise = createDeferredWithTimeout<TStream>(timeout, () => tap(new StreamError('Subscribe timeout'), () => abortController.abort()))

    const resolve = (stream: TStream, callback?: () => void) => {
        if (!promise.isSettled) {
            promise.resolve(stream)
            callback?.()
        }
    }

    const reject = (error: unknown, callback?: () => void) => (promise.isSettled ? callback?.() : promise.reject(error))
    const handleAbort = () => reject(abortSignal.reason)
    const handlers: Record<string, Fn> = {}

    abortSignal.addEventListener('abort', handleAbort, { once: true })

    try {
        const stream = await createStream(abortSignal)

        cleanup = () => {
            abortSignal.removeEventListener('abort', handleAbort)

            for (const [event, handler] of Object.entries(handlers)) {
                stream.removeListener(event, handler)
            }
        }

        stream.once('metadata', handlers['metadata'] = (metadata) => resolve(stream, () => onMetadata?.(metadata)))
        stream.once('status', handlers['status'] = (status) => reject(new StreamError('Stream status received before subscription completed').withValue('status', status), () => onStatus?.(status)))

        stream.on('error', handlers['error'] = (error) => reject(error, () => onError?.(error)))
        stream.on('pause', handlers['pause'] = () => promise.isSettled && onPause?.())
        stream.on('resume', handlers['resume'] = () => promise.isSettled && onResume?.())
        stream.on('data', handlers['data'] = (data) => (promise.isSettled ? onData?.(data) : promise.resolve(stream)))

        stream.on('end', handlers['end'] = () => {
            // TODO: Implement end handling
            onEnd?.()
        })

        stream.on('close', handlers['close'] = () => {
            cleanup?.()

            if (promise.isSettled) {
                onClose?.()
            } else {
                promise.reject(new StreamError('Stream closed before subscription completed'))
            }
        })
    } catch (error) {
        if (!isAbortError(error) && !promise.isSettled) {
            promise.reject(error)
        }
    }

    return promise.finally(() => cleanup?.())
}
