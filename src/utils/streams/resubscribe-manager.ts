import type { ClientReadableStream, StatusObject } from '@grpc/grpc-js'
import { notNullish } from '@kdt310722/utils/common'
import type { Emitter } from '@kdt310722/utils/event'
import { transform } from '@kdt310722/utils/function'
import { sleep } from '@kdt310722/utils/promise'
import type { OnStreamChangeFn, StreamWithClose } from './types'

export type CreateStreamFn<TStream extends ClientReadableStream<any>> = (onStreamChange?: OnStreamChangeFn<TStream>) => Promise<StreamWithClose<TStream>>

export interface ResubscribeManagerOptions {
    enabled?: boolean
    retries?: number
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
    autoReset?: boolean
    circuitBreakerTimeout?: number
    shouldResubscribe?: (error?: unknown, status?: StatusObject) => boolean
}

export type ResubscribeManagerEvents = {
    waitForResubscribe: (delay: number) => void
    resubscribe: (attempt: number, retriesLeft: number) => void
    resubscribed: () => void
    resubscribeAbandoned: () => void
    circuitBreakerTripped: (lastResubscribeSuccessTime: number) => void
    error: (error: unknown) => void
}

export const DEFAULT_RESUBSCRIBE_MANAGER_OPTIONS: Required<ResubscribeManagerOptions> = {
    enabled: true,
    retries: 5,
    delay: 1000,
    backoff: 2,
    jitter: 0,
    maxDelay: 10_000,
    autoReset: true,
    circuitBreakerTimeout: 60_000,
    shouldResubscribe: () => true,
}

export class ResubscribeManager<TStream extends ClientReadableStream<any>> {
    protected readonly options: Required<ResubscribeManagerOptions>

    protected latestError?: unknown
    protected latestStatus?: StatusObject

    protected resubscribeAttempt = 0
    protected isResubscribing = false
    protected lastResubscribeSuccessTime?: number

    public constructor(options: ResubscribeManagerOptions, protected readonly emitter: Emitter<ResubscribeManagerEvents>, protected readonly createStream: CreateStreamFn<TStream>) {
        this.options = { ...DEFAULT_RESUBSCRIBE_MANAGER_OPTIONS, ...options }
    }

    public setLatestError(error: unknown) {
        return (this.latestError = error)
    }

    public setLatestStatus(status: StatusObject) {
        return (this.latestStatus = status)
    }

    public reset() {
        this.resubscribeAttempt = 0
        this.lastResubscribeSuccessTime = undefined
        this.latestError = undefined
        this.latestStatus = undefined
    }

    public handleResubscribe(stream: TStream, onStreamChange?: OnStreamChangeFn<TStream>) {
        if (this.isResubscribing || !this.options.enabled || !this.options.shouldResubscribe(this.latestError, this.latestStatus)) {
            return this.emitter.emit('resubscribeAbandoned')
        }

        if (notNullish(this.lastResubscribeSuccessTime) && Date.now() - this.lastResubscribeSuccessTime < this.options.circuitBreakerTimeout) {
            this.emitter.emit('circuitBreakerTripped', this.lastResubscribeSuccessTime)
            this.emitter.emit('resubscribeAbandoned')

            return true
        }

        this.isResubscribing = true

        const handleError = (error: unknown) => {
            this.emitter.emit('error', error)
            this.emitter.emit('resubscribeAbandoned')
        }

        this.resubscribe(stream, onStreamChange).then((newStream) => this.handleResubscribeResult(newStream, onStreamChange)).catch(handleError).finally(() => {
            this.isResubscribing = false
        })

        return true
    }

    protected async resubscribe(stream: TStream, onStreamChange?: OnStreamChangeFn<TStream>): Promise<StreamWithClose<TStream> | null> {
        if (this.resubscribeAttempt >= this.options.retries) {
            return null
        }

        const delay = this.getResubscribeDelay(++this.resubscribeAttempt)

        if (delay > 0) {
            this.emitter.emit('waitForResubscribe', delay)
        }

        await sleep(delay).then(() => this.emitter.emit('resubscribe', this.resubscribeAttempt, this.options.retries - this.resubscribeAttempt))

        return this.createStream((newStream) => onStreamChange?.(newStream)).catch((error) => {
            if (!this.options.shouldResubscribe(error, this.latestStatus)) {
                return null
            }

            return this.resubscribe(stream, onStreamChange)
        })
    }

    protected handleResubscribeResult(newStream: StreamWithClose<TStream> | null, onStreamChange?: OnStreamChangeFn<TStream>) {
        onStreamChange?.(newStream)

        if (notNullish(newStream)) {
            this.lastResubscribeSuccessTime = Date.now()

            if (this.options.autoReset) {
                this.resubscribeAttempt = 0
            }

            this.emitter.emit('resubscribed')
        } else {
            this.emitter.emit('resubscribeAbandoned')
        }
    }

    protected getResubscribeDelay(attempts: number) {
        const baseDelay = Math.min(this.options.delay * this.options.backoff ** (attempts - 1), this.options.maxDelay)

        if (this.options.jitter > 0) {
            return transform(baseDelay * this.options.jitter, (jitter) => baseDelay - (jitter / 2) + (Math.random() * jitter))
        }

        return baseDelay
    }
}
