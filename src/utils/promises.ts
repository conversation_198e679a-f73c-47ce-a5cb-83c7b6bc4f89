import { type CreateDeferredOptions, type DeferredPromise, createDeferred } from '@kdt310722/utils/promise'

export function createDeferredWithTimeout<T>(timeout: number, error?: string | Error | (() => string | Error), options: CreateDeferredOptions = {}): DeferredPromise<T> {
    let timer: NodeJS.Timeout | undefined

    const deferred = createDeferred<T>({
        ...options,
        onSettle: () => {
            if (timer) {
                clearTimeout(timer)
                timer = undefined
            }

            options.onSettle?.()
        },
    })

    const getError = () => {
        if (typeof error === 'function') {
            error = error()
        }

        return error instanceof Error ? error : new Error(error)
    }

    timer = setTimeout(() => !deferred.isSettled && deferred.reject(getError()), timeout)

    return deferred
}
